# 🛡️📄 Insurance Policy Scrapper - Streamlit Interface

This document provides instructions for setting up and running the Streamlit web interface for the Insurance Policy Scrapper application.

## 🚀 Quick Start

### Option 1: Using the Runner Script (Recommended)
```bash
python run_streamlit.py
```

### Option 2: Direct Streamlit Command
```bash
streamlit run streamlit_show.py
```

## 📋 Prerequisites

1. **Python 3.12+** installed
2. **All project dependencies** installed:
   ```bash
   pip install -r requirements.txt
   ```
3. **Environment variables** configured (`.env` file)

## 🔧 Setup Instructions

### 1. Install Dependencies
```bash
# Install all required packages including Streamlit
pip install -r requirements.txt
```

### 2. Configure Environment
Ensure your `.env` file contains the necessary configuration:
```env
AZURE_OPENAI_ENDPOINT=your_endpoint
AZURE_OPENAI_API_KEY=your_api_key
AZURE_OPENAI_API_VERSION=2025-01-01-preview
provider=openai
embedding_provider=openai
openai_model_name=gpt-4.1
```

### 3. Run the Application
```bash
# Using the runner script
python run_streamlit.py

# Or directly with streamlit
streamlit run streamlit_show.py --server.port 8501
```

## 🌐 Accessing the Application

Once started, the application will be available at:
- **Local URL:** http://localhost:8501
- **Network URL:** http://your-ip:8501 (if accessible from network)

## 📱 Features

### 🔄 User Interface
- **Clean, intuitive design** with professional styling
- **Responsive layout** that works on different screen sizes
- **Progress indicators** for processing status
- **Error handling** with user-friendly messages

### 📁 File Upload
- **PDF file validation** (format, size limits)
- **Drag-and-drop support** for easy file upload
- **File size limit:** 50MB maximum
- **Automatic file naming** to prevent conflicts

### 🤖 Processing
- **Real-time progress tracking** with visual indicators
- **Status updates** during different processing phases
- **Error recovery** with detailed error messages
- **Background processing** with progress visualization

### 📊 Results Display
- **Interactive data table** showing extracted fields
- **Metrics dashboard** with processing statistics
- **Download options** for both Excel and JSON formats
- **Formatted Excel output** with styling

### 💾 Download Options
- **Excel file** (.xlsx) with formatted data
- **JSON file** (.json) with raw extracted data
- **Timestamped filenames** to prevent overwrites

## 🎛️ Configuration

### Sidebar Information
The sidebar displays:
- **Usage instructions**
- **Supported file formats**
- **Current configuration** (model provider, root folder)
- **Application features**

### Session Management
- **Persistent session state** across interactions
- **Reset functionality** to process new documents
- **Error state management** with recovery options

## 🐛 Troubleshooting

### Common Issues

1. **"Streamlit not found" error:**
   ```bash
   pip install streamlit==1.40.2
   ```

2. **"Module not found" errors:**
   ```bash
   pip install -r requirements.txt
   ```

3. **Port already in use:**
   ```bash
   streamlit run streamlit_show.py --server.port 8502
   ```

4. **File upload issues:**
   - Check file size (max 50MB)
   - Ensure file is a valid PDF
   - Check folder permissions

### Debug Mode
To run in debug mode with more verbose output:
```bash
streamlit run streamlit_show.py --logger.level debug
```

## 🔒 Security Considerations

- **File validation** prevents malicious uploads
- **Size limits** prevent resource exhaustion
- **Temporary file cleanup** after processing
- **Error message sanitization** to prevent information leakage

## 📈 Performance Tips

1. **File Size:** Keep PDF files under 10MB for optimal performance
2. **Browser:** Use modern browsers (Chrome, Firefox, Safari)
3. **Network:** Stable internet connection for API calls
4. **Resources:** Ensure adequate system memory for large documents

## 🔄 Updates and Maintenance

### Updating Dependencies
```bash
pip install -r requirements.txt --upgrade
```

### Clearing Cache
```bash
streamlit cache clear
```

### Logs
Application logs are stored in:
- **Log file:** `logs/app.log`
- **Streamlit logs:** Console output

## 📞 Support

For issues or questions:
1. Check the troubleshooting section above
2. Review application logs
3. Ensure all dependencies are correctly installed
4. Verify environment configuration

## 🎯 Next Steps

After setting up the Streamlit interface:
1. Test with sample PDF documents
2. Verify output quality and formatting
3. Configure any additional settings as needed
4. Consider deploying to a web server for team access

---

**Happy Processing! 🚀**
