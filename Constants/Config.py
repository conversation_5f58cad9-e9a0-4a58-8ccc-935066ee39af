"""
Configuration management for the Insurance Policy Classification system.
"""

from functools import lru_cache
from pathlib import Path
from typing import Optional

from dotenv import load_dotenv
from pydantic import Field
from pydantic_settings import BaseSettings

from utils.LogUtils import logger

# Load environment variables once
load_dotenv()


class Settings(BaseSettings):
    """Application settings with environment variable support."""

    # Model Configuration
    provider: str = Field(default="openai", description="Model provider: 'openai', 'ollama', or 'huggingface'")
    embedding_provider: str = Field(default="openai", description="Embedding model provider")

    # OpenAI Configuration
    openai_model_name: str = Field(default="gpt-4.1", description="OpenAI model name")
    openai_embedding_model: str = Field(default="text-embedding-3-small", description="OpenAI embedding model")
    azure_openai_endpoint: Optional[str] = Field(default=None, description="Azure OpenAI endpoint")
    azure_openai_api_key: Optional[str] = Field(default=None, description="Azure OpenAI API key")
    azure_openai_api_version: str = Field(default="2023-12-01-preview", description="Azure OpenAI API version")

    # Ollama Configuration
    ollama_model_name: str = Field(default="llama2", description="Ollama model name")
    ollama_embedding_model: str = Field(default="nomic-embed-text", description="Ollama embedding model")
    ollama_base_url: str = Field(default="http://localhost:11434", description="Ollama base URL")

    # Data Paths
    root_folder: str = Field(default="./data/policy_docs", description="Root folder for policy documents")
    output_folder: str = Field(default="./data/outputs", description="Output folder for policy documents")
    temp_vector_store_path: str = Field(default="./temp/faiss_collection", description="Temporary vector store path")

    # Processing Configuration
    top_k_value: int = Field(default=3, ge=1, le=20, description="Number of top results to retrieve")
    max_context_classification: int = Field(default=6000, ge=1000, description="Maximum context tokens")
    max_context_extraction: int = Field(default=12000, ge=1000, description="Maximum context tokens")
    min_chunk_relevance_score: float = Field(default=0.3, ge=0.0, le=1.0, description="Minimum chunk relevance score")
    enable_context_deduplication: bool = Field(default=True, description="Enable context deduplication")
    enable_schema_validation: bool = Field(default=True, description="Enable schema validation")

    # Logging
    log_level: str = Field(default="INFO", description="Logging level")
    log_file: str = Field(default="logs/app.log", description="Log file path")

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
        env_prefix = ""
        validate_assignment = True
        extra = "allow"


    @property
    def model_provider(self) -> str:
        """Alias for provider to maintain backward compatibility."""
        return self.provider

    def get_model_config(self) -> dict:
        """Get model configuration based on provider."""
        if self.provider == "openai":
            return {
                "model_name": self.openai_model_name,
                "embedding_model": self.openai_embedding_model,
                "api_key": self.azure_openai_api_key,
                "endpoint": self.azure_openai_endpoint,
                "api_version": self.azure_openai_api_version,
            }
        elif self.provider == "ollama":
            return {
                "model_name": self.ollama_model_name,
                "embedding_model": self.ollama_embedding_model,
                "base_url": self.ollama_base_url,
            }
        else:
            raise ValueError(f"Unknown provider: {self.provider}")


@lru_cache(maxsize=1)
def get_settings() -> Settings:
    """Get application settings (cached singleton)."""
    try:
        settings = Settings()

        # Validate critical configurations
        _validate_provider_config(settings)
        # Ensure required directories exist
        ensure_directories(settings)

        logger.info(f"Configuration loaded successfully. Provider: {settings.provider}")
        return settings

    except Exception as e:
        logger.error(f"Failed to load configuration: {e}")
        raise


def _validate_provider_config(settings: Settings) -> None:
    """Validate provider-specific configuration."""
    if settings.provider == "openai":
        if not settings.azure_openai_api_key:
            logger.warning("OpenAI API key not provided")
        if not settings.azure_openai_endpoint:
            logger.warning("Azure OpenAI endpoint not provided")


def ensure_directories(settings: Settings) -> None:
    """Ensure required directories exist."""
    directories = [
        Path(settings.temp_vector_store_path).parent,
        Path(settings.log_file).parent,
        Path(settings.root_folder),
        Path(settings.output_folder),
    ]

    for directory in directories:
        try:
            directory.mkdir(parents=True, exist_ok=True)
            logger.debug(f"Ensured directory exists: {directory}")
        except Exception as e:
            logger.error(f"Failed to create directory {directory}: {e}")
            raise


def reload_settings() -> Settings:
    """Force reload settings (clears cache)."""
    get_settings.cache_clear()
    return get_settings()


# Initialize settings lazily
def get_current_settings() -> Settings:
    """Get current settings instance."""
    return get_settings()


# Export commonly used settings for backward compatibility
settings = get_settings()
