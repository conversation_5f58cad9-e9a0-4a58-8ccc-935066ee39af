import streamlit as st
import pandas as pd
import tempfile
import os
import time
from pathlib import Path
from typing import Optional

from Constants.Config import settings
from InfoExtractor.policy_scrapper import process_policy_docs
from utils.LogUtils import logger


# --- Page Configuration ---
st.set_page_config(
    page_title="Insurance Policy Scrapper",
    page_icon="🛡️",
    layout="wide",
    initial_sidebar_state="expanded"
)

# --- Custom CSS ---
st.markdown("""
<style>
    .main-header {
        text-align: center;
        color: #1f77b4;
        margin-bottom: 2rem;
    }
    .upload-section {
        border: 2px dashed #1f77b4;
        border-radius: 10px;
        padding: 2rem;
        text-align: center;
        margin: 1rem 0;
    }
    .success-message {
        background-color: #d4edda;
        border: 1px solid #c3e6cb;
        color: #155724;
        padding: 1rem;
        border-radius: 5px;
        margin: 1rem 0;
    }
    .error-message {
        background-color: #f8d7da;
        border: 1px solid #f5c6cb;
        color: #721c24;
        padding: 1rem;
        border-radius: 5px;
        margin: 1rem 0;
    }
    .info-box {
        background-color: #e7f3ff;
        border: 1px solid #b3d9ff;
        color: #0c5460;
        padding: 1rem;
        border-radius: 5px;
        margin: 1rem 0;
    }
</style>
""", unsafe_allow_html=True)

# --- Session State Initialization ---
def initialize_session_state():
    """Initialize all session state variables"""
    session_vars = {
        'pdf_uploaded': False,
        'excel_path': None,
        'pdf_path': None,
        'processing': False,
        'processing_complete': False,
        'error_message': None,
        'json_result': None,
        'upload_success': False
    }

    for var, default_value in session_vars.items():
        if var not in st.session_state:
            st.session_state[var] = default_value

initialize_session_state()

# --- Helper Functions ---
def validate_pdf_file(uploaded_file) -> tuple[bool, str]:
    """Validate uploaded PDF file"""
    if uploaded_file is None:
        return False, "No file uploaded"

    if not uploaded_file.name.lower().endswith('.pdf'):
        return False, "Please upload a PDF file only"

    if uploaded_file.size > 50 * 1024 * 1024:  # 50MB limit
        return False, "File size too large. Please upload a file smaller than 50MB"

    return True, "File is valid"

def save_uploaded_file(uploaded_file) -> tuple[bool, str, Optional[str]]:
    """Save uploaded file to the designated folder"""
    try:
        # Ensure the root folder exists
        os.makedirs(settings.root_folder, exist_ok=True)

        filename = uploaded_file.name
        save_path = os.path.join(settings.root_folder, filename)

        # Check if file already exists and create unique name if needed
        counter = 1
        original_path = save_path
        while os.path.exists(save_path):
            name, ext = os.path.splitext(original_path)
            save_path = f"{name}_{counter}{ext}"
            counter += 1

        with open(save_path, "wb") as f:
            f.write(uploaded_file.read())

        logger.info(f"File saved successfully: {save_path}")
        return True, f"File saved as: {os.path.basename(save_path)}", save_path

    except Exception as e:
        logger.error(f"Error saving file: {e}")
        return False, f"Error saving file: {str(e)}", None

def reset_session():
    """Reset all session state variables"""
    st.session_state.pdf_uploaded = False
    st.session_state.excel_path = None
    st.session_state.pdf_path = None
    st.session_state.processing = False
    st.session_state.processing_complete = False
    st.session_state.error_message = None
    st.session_state.json_result = None
    st.session_state.upload_success = False

# --- Main Application ---
def main():
    """Main application function"""

    # --- Header ---
    st.markdown('<h1 class="main-header">🛡️📄 Insurance Policy Scrapper</h1>', unsafe_allow_html=True)

    # --- Sidebar Information ---
    with st.sidebar:
        st.header("ℹ️ Information")
        st.markdown("""
        **How to use:**
        1. Upload a PDF policy document
        2. Click 'Process Document' to analyze
        3. View and download the extracted data

        **Supported formats:**
        - PDF files only
        - Maximum size: 50MB

        **Features:**
        - Automatic field extraction
        - Excel output generation
        - Data validation
        """)

        st.header("⚙️ Settings")
        st.info(f"**Model Provider:** {settings.model_provider}")
        st.info(f"**Root Folder:** {settings.root_folder}")

    # --- Error Display ---
    if st.session_state.error_message:
        st.markdown(f'<div class="error-message">❌ {st.session_state.error_message}</div>',
                   unsafe_allow_html=True)

    # --- Upload Section ---
    st.markdown('<div class="upload-section">', unsafe_allow_html=True)
    st.subheader("📁 Upload Policy Document")

    uploaded_file = st.file_uploader(
        "Choose a PDF file",
        type="pdf",
        help="Upload your insurance policy document in PDF format"
    )

    if uploaded_file:
        # Validate file
        is_valid, message = validate_pdf_file(uploaded_file)

        if is_valid:
            if not st.session_state.upload_success:
                # Save file
                success, save_message, save_path = save_uploaded_file(uploaded_file)

                if success:
                    st.session_state.pdf_path = save_path
                    st.session_state.pdf_uploaded = True
                    st.session_state.upload_success = True
                    st.session_state.error_message = None
                    st.markdown(f'<div class="success-message">✅ {save_message}</div>',
                               unsafe_allow_html=True)
                else:
                    st.session_state.error_message = save_message
                    st.rerun()
            else:
                st.markdown(f'<div class="success-message">✅ File uploaded: {uploaded_file.name}</div>',
                           unsafe_allow_html=True)
        else:
            st.session_state.error_message = message
            st.rerun()

    st.markdown('</div>', unsafe_allow_html=True)

    # --- Processing Section ---
    if st.session_state.pdf_uploaded and not st.session_state.processing_complete:
        st.subheader("🔄 Process Document")

        col1, col2, col3 = st.columns([1, 2, 1])

        with col2:
            if not st.session_state.processing:
                if st.button("🚀 Process Document", type="primary", use_container_width=True):
                    st.session_state.processing = True
                    st.session_state.error_message = None
                    st.rerun()
            else:
                st.markdown('<div class="info-box">⏳ Processing document... Please wait.</div>',
                           unsafe_allow_html=True)

                # Create progress bar
                progress_bar = st.progress(0)
                status_text = st.empty()

                try:
                    # Simulate progress updates
                    for i in range(100):
                        progress_bar.progress(i + 1)
                        if i < 20:
                            status_text.text("📖 Reading document...")
                        elif i < 50:
                            status_text.text("🔍 Extracting information...")
                        elif i < 80:
                            status_text.text("📊 Processing data...")
                        else:
                            status_text.text("✨ Finalizing results...")
                        time.sleep(0.05)  # Small delay for visual effect

                    # Actual processing
                    status_text.text("🤖 Running AI analysis...")
                    result = process_policy_docs(settings.root_folder, False)

                    # Update session state
                    st.session_state.json_result = result['result']
                    st.session_state.excel_path = result['output_file_path']
                    st.session_state.processing = False
                    st.session_state.processing_complete = True

                    progress_bar.progress(100)
                    status_text.text("✅ Processing complete!")

                    st.success("Document processed successfully!")
                    time.sleep(1)
                    st.rerun()

                except Exception as e:
                    logger.error(f"Processing error: {e}")
                    st.session_state.error_message = f"Processing failed: {str(e)}"
                    st.session_state.processing = False
                    st.rerun()

    # --- Results Section ---
    if st.session_state.processing_complete and st.session_state.excel_path:
        st.subheader("📊 Extraction Results")

        try:
            # Load and display Excel data
            df = pd.read_excel(st.session_state.excel_path)

            # Display metrics
            col1, col2, col3 = st.columns(3)
            with col1:
                st.metric("Fields Extracted", len(df))
            with col2:
                st.metric("Document Status", "✅ Processed")
            with col3:
                st.metric("Output Format", "Excel")

            # Display data
            st.dataframe(df, use_container_width=True)

            # Download section
            st.subheader("💾 Download Results")

            col1, col2 = st.columns(2)

            with col1:
                # Excel download
                with open(st.session_state.excel_path, "rb") as file:
                    st.download_button(
                        label="📥 Download Excel File",
                        data=file.read(),
                        file_name=f"policy_extraction_{int(time.time())}.xlsx",
                        mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                        type="primary"
                    )

            with col2:
                # JSON download (if available)
                if st.session_state.json_result:
                    import json
                    json_str = json.dumps(st.session_state.json_result, indent=2)
                    st.download_button(
                        label="📥 Download JSON Data",
                        data=json_str,
                        file_name=f"policy_extraction_{int(time.time())}.json",
                        mime="application/json"
                    )

        except Exception as e:
            logger.error(f"Error displaying results: {e}")
            st.error(f"Error loading results: {str(e)}")

    # --- Reset Section ---
    if st.session_state.pdf_uploaded or st.session_state.processing_complete:
        st.markdown("---")
        col1, col2, col3 = st.columns([1, 1, 1])

        with col2:
            if st.button("🔄 Process New Document", type="secondary", use_container_width=True):
                reset_session()
                st.rerun()


if __name__ == "__main__":
    main()
