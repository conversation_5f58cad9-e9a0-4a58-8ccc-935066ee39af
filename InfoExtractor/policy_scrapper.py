import os
import time
from pathlib import Path

import pandas as pd
from langchain_core.messages import SystemMessage, HumanMessage

from Constants.Config import settings
from Constants.Prompts import system_prompt_policy_classification, human_prompt_policy_classification
from Constants.SearchTerms import queries_for_context
from Dal.VectorStoreDal import retrieve_context_hybrid, sort_and_deduplicate_chunks
from Dal.retriever_store import init_retrievers, get_retriever, get_bm25_retriever
from InfoExtractor.FieldsExtractor import extract_key_fields
from OCR.Pdf_Text_Extractor import PDFOCRReader, pdf_to_images_base64
from llm.SetupLLM_Embeddings import get_models
from utils.DataUtils import limit_chunks_by_max_tokens, format_to_json, format_output_excel
from utils.LogUtils import logger

result = {}


def process_policy_docs(root_folder: str, send_pdf=True):
    """ Process Policy Documents to Classify and Extract Information """
    global output_path, result, result_file_path, predicted_policy
    try:
        start_time = time.time()

        # Step 1: Get all PDF files.
        root_path = Path(root_folder)
        if not root_path.exists():
            logger.error(f"Root folder {root_folder} does not exist.")
            return None
        else:
            document_paths = list(Path(root_path).rglob("*.pdf"))
            logger.info(f"Found {len(document_paths)} PDF files in {root_folder}")

        # Step 2: OCR PDF file 1 by 1
        for document_path in document_paths:
            logger.info(f"Passing Documents to OCR")
            filename = str(os.path.splitext(os.path.basename(document_path))[0])
            pdf_reader = PDFOCRReader(extractor_type="compare")  # or "ocr", "fitz", "pdfplumber", "compare", "mistral"
            pdf_pages = pdf_reader.load_pdf_documents(str(document_path))
            logger.info(f"Found {len(pdf_pages)} pages in {document_path}")

            # Step 3: Create and store pdf_pages in VectorDB
            try:
                llm, embeddings = get_models(settings)

                init_retrievers(pdf_pages, embeddings)
                # current_retriever = get_retriever()
                # current_bm25_retriever = get_bm25_retriever()
                logger.info("Vector store and retrievers created successfully")
            except Exception as e:
                logger.error(f"Error creating vector store and retrievers: {e}")
                return {
                    "status": "Failed",
                    "policy_type": None,
                    "result": None,
                    "output_file_path": None
                }

            # Step 4: Retrieve relevant documents
            comprehensive_context = []
            logger.info("📚 Using Hybrid Retrieval approach (Vector + BM25)")
            for q_idx, primary_query in enumerate(queries_for_context):
                retrieved_pages = retrieve_context_hybrid.invoke(primary_query,vector_weight=0.7, bm25_weight=0.3)
                comprehensive_context.extend(retrieved_pages)

            logger.info(f"Found {len(comprehensive_context)} relevant documents.")

            comprehensive_context = sort_and_deduplicate_chunks(comprehensive_context)
            filtered_context = limit_chunks_by_max_tokens(comprehensive_context, settings.max_context_classification)

            if not filtered_context:
                logger.warning("No relevant context found after filtering")

            # Step 5: Prepare Data for LLM
            context_parts = []
            for doc in filtered_context:
                page_num = doc.metadata.get('page_number', 'Unknown')
                content = doc.page_content.strip()
                context_parts.append(f"[File Name {doc.metadata.get('source', 'Unknown')}, Page {page_num}]: {content}")
                logger.info(f"[File Name {doc.metadata.get('source', 'Unknown')}, Page {page_num}]")

            final_context = "\n\n".join(context_parts)
            logger.info(f"Final context length: {len(final_context)} characters")

            # Step 6: Prompts
            system_prompt = system_prompt_policy_classification
            human_prompt = human_prompt_policy_classification.format(final_context=final_context)
            human_message = [{"type": "text", "text": human_prompt}]
            if send_pdf: # Upload PDF pages to Model
                for doc in filtered_context:
                    data_url = pdf_to_images_base64(doc.metadata['file_path'], page_number=doc.metadata['page_number'])
                    human_message.append({"type": "image_url", "image_url": {"url": f"data:image/png;base64,{data_url}"} })

            chat_history = [SystemMessage(content=system_prompt), HumanMessage(content=human_message)]

            # Step 7: LLM call for Classification
            max_retries = 2
            for attempt in range(max_retries):
                try:
                    logger.info(f"LLM invocation attempt {attempt + 1}/{max_retries}")

                    raw_response = llm.invoke(chat_history)
                    raw_content = raw_response.content if hasattr(raw_response, 'content') else str(raw_response)
                    logger.info(f"Response Metadata: {raw_response.response_metadata['token_usage']}")
                    classification_result = format_to_json(raw_content)
                    if classification_result[1]:
                        predicted_policy = classification_result[0].get("full_category_name")
                        predicted_number = classification_result[0].get("policy_number")
                        logger.info(f"Predicted Policy: {predicted_policy} and Policy Number: {predicted_number}")
                        predicted_policy = is_predicted_policy_correct(predicted_number, predicted_policy, "data/Policy_Number_to_Doc_Mapping.xlsx")
                        break
                    else:
                        continue
                except Exception as e:
                    logger.error(f"Error in attempt {attempt + 1}: {str(e)}")
                    if attempt < max_retries - 1:
                        continue
            else:
                logger.error(f"Failed to classify after {max_retries} attempts.")
                continue

            #step 8: Extract Key-Value Pairs
            result = extract_key_fields(predicted_policy, llm, document_path, True)
    except Exception as e:
        logger.error(f"Error processing policy documents: {e}")
        return {
            "status": "Failed",
            "policy_type": None,
            "result": None,
            "output_file_path": None
        }
    else:
        logger.info(f"Total time taken: {time.time() - start_time} seconds")
        return result


def is_predicted_policy_correct(policy_number: str, predicted_policy: str, excel_path: str) -> str:
    def pattern_matches(pattern: str, policy_number: str) -> bool:
        only_letters = ''.join(c for c in policy_number if c.isalpha()).upper()
        return pattern.upper() == only_letters[:len(pattern)]

    try:
        df = pd.read_excel(excel_path)

        for _, row in df.iterrows():
            pattern = str(row["Pattern to Match against Policy #"]).strip()
            doc_type = str(row["Document Type"]).strip()
            if pattern_matches(pattern, policy_number):
                if doc_type.lower() == predicted_policy.strip().lower():
                    logger.debug("Predicted Policy is Correct")
                    return predicted_policy
                else:
                    logger.debug(f"Predicted Policy is Incorrect : {doc_type}")
                    return doc_type
        return predicted_policy
    except Exception as e:
        logger.warning(f"Error: {e}")
        return predicted_policy
